from typing import Dict, List, Optional, Any, Union
from langchain_core.messages import HumanMessage, AIMessage
import logging

from agents.rag_agent import MedicalRAG
from agents.image_analysis_agent import ImageAnalysisAgent


class MultimodalRAGAgent:
    """
    Agent that combines image analysis with RAG retrieval for multimodal medical queries.
    Handles text + image input by:
    1. Analyzing the medical image using appropriate specialist agent
    2. Converting image analysis results to text context
    3. Combining text context with original query for enhanced RAG retrieval
    4. Falling back to web search if confidence is low
    """
    
    def __init__(self, config):
        self.config = config
        self.rag_agent = MedicalRAG(config)
        self.image_analyzer = ImageAnalysisAgent(config)
        
        # Configure logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
    
    def process_multimodal_query(self, text_query: str, image_path: str, image_type: str, 
                                chat_history: Optional[List] = None) -> Dict[str, Any]:
        """
        Process a multimodal query containing both text and image.
        
        Args:
            text_query: The user's text question
            image_path: Path to the uploaded medical image
            image_type: Type of medical image (BRAIN MRI SCAN, CHEST X-RAY, SKIN LESION)
            chat_history: Previous conversation context
            
        Returns:
            Dictionary containing response, confidence, and metadata
        """
        try:
            self.logger.info(f"Processing multimodal query: '{text_query}' with image type: {image_type}")
            
            # Step 1: Analyze the medical image
            image_context = self._analyze_medical_image(image_path, image_type)
            
            # Step 2: Create enhanced query by combining text + image context
            enhanced_query = self._create_enhanced_query(text_query, image_context)
            
            # Step 3: Perform RAG retrieval with enhanced query
            rag_result = self._perform_rag_retrieval(enhanced_query, chat_history)
            
            # Step 4: Combine image analysis with RAG results
            final_response = self._combine_results(text_query, image_context, rag_result)
            
            return {
                'response': final_response['response'],
                'confidence': rag_result.get('confidence', 0.0),
                'image_analysis': image_context,
                'rag_confidence': rag_result.get('confidence', 0.0),
                'sources': rag_result.get('sources', []),
                'insufficient_info': rag_result.get('insufficient_info', False)
            }
            
        except Exception as e:
            self.logger.error(f"Error in multimodal processing: {str(e)}")
            return {
                'response': f"Error processing multimodal query: {str(e)}",
                'confidence': 0.0,
                'image_analysis': {},
                'rag_confidence': 0.0,
                'sources': [],
                'insufficient_info': True
            }
    
    def _analyze_medical_image(self, image_path: str, image_type: str) -> Dict[str, Any]:
        """Analyze medical image using appropriate specialist agent."""
        try:
            if image_type == "BRAIN MRI SCAN":
                from agents.image_analysis_agent.brain_tumor_agent.brain_tumor_inference import BrainTumorInference
                brain_analyzer = BrainTumorInference()
                analysis = brain_analyzer.analyze_mri(image_path)
                return self._format_brain_analysis(analysis)
                
            elif image_type == "CHEST X-RAY":
                result = self.image_analyzer.classify_chest_xray(image_path)
                return self._format_chest_analysis(result)
                
            elif image_type == "SKIN LESION":
                result = self.image_analyzer.segment_skin_lesion(image_path)
                return self._format_skin_analysis(result)
                
            else:
                return {"description": f"Medical image of type {image_type} detected but no specific analysis available."}
                
        except Exception as e:
            self.logger.error(f"Error analyzing image: {str(e)}")
            return {"description": f"Error analyzing {image_type} image: {str(e)}"}
    
    def _format_brain_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Format brain MRI analysis results into text context."""
        if analysis.get('has_tumor', False):
            tumor_type = analysis.get('tumor_type', 'unknown')
            confidence = analysis.get('confidence', 0.0) * 100
            description = f"Brain MRI analysis shows presence of {tumor_type} tumor with {confidence:.1f}% confidence."
        else:
            confidence = analysis.get('confidence', 0.0) * 100
            description = f"Brain MRI analysis shows no tumor detected with {confidence:.1f}% confidence."
        
        return {
            "description": description,
            "analysis_type": "brain_mri",
            "details": analysis
        }
    
    def _format_chest_analysis(self, result: str) -> Dict[str, Any]:
        """Format chest X-ray analysis results into text context."""
        if result == "covid19":
            description = "Chest X-ray analysis indicates potential COVID-19 related abnormalities."
        else:
            description = "Chest X-ray analysis shows normal findings."
        
        return {
            "description": description,
            "analysis_type": "chest_xray",
            "classification": result
        }
    
    def _format_skin_analysis(self, result: str) -> Dict[str, Any]:
        """Format skin lesion analysis results into text context."""
        description = f"Skin lesion analysis completed with result: {result}"
        
        return {
            "description": description,
            "analysis_type": "skin_lesion",
            "result": result
        }
    
    def _create_enhanced_query(self, text_query: str, image_context: Dict[str, Any]) -> str:
        """Combine text query with image analysis context."""
        image_description = image_context.get('description', '')
        
        enhanced_query = f"""
        User Question: {text_query}
        
        Medical Image Analysis Context: {image_description}
        
        Please provide a comprehensive answer considering both the user's question and the medical image analysis results.
        """
        
        return enhanced_query.strip()
    
    def _perform_rag_retrieval(self, enhanced_query: str, chat_history: Optional[List] = None) -> Dict[str, Any]:
        """Perform RAG retrieval with the enhanced query."""
        try:
            # Convert chat_history format if needed
            formatted_history = []
            if chat_history:
                for msg in chat_history[-self.config.rag.context_limit:]:
                    if isinstance(msg, HumanMessage):
                        formatted_history.append({"role": "user", "content": msg.content})
                    elif isinstance(msg, AIMessage):
                        formatted_history.append({"role": "assistant", "content": msg.content})
            
            # Use the RAG agent to process the enhanced query
            result = self.rag_agent.process_query(enhanced_query, formatted_history)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in RAG retrieval: {str(e)}")
            return {
                'response': f"Error in knowledge retrieval: {str(e)}",
                'confidence': 0.0,
                'sources': [],
                'insufficient_info': True
            }
    
    def _combine_results(self, original_query: str, image_context: Dict[str, Any], 
                        rag_result: Dict[str, Any]) -> Dict[str, Any]:
        """Combine image analysis and RAG results into final response."""
        
        image_description = image_context.get('description', '')
        rag_response = rag_result.get('response', '')
        
        # Create comprehensive response
        combined_response = f"""Based on your question and the medical image analysis:

**Image Analysis:** {image_description}

**Medical Knowledge:** {rag_response}

This response combines analysis of your medical image with relevant medical literature to provide comprehensive information."""
        
        return {
            'response': combined_response,
            'confidence': rag_result.get('confidence', 0.0)
        }
