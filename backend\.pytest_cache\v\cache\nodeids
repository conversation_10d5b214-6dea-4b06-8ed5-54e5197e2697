["tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_confidence_based_fallback", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_error_handling_in_agent_execution", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_image_agent_mapping", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_input_handler_image_validation", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_input_handler_multimodal_input", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_input_handler_text_only", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_input_type_description", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_multimodal_agent_initialization", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_response_combination", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_routing_logic_high_confidence_image", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_routing_logic_low_confidence_image", "tests/test_multimodal_rag.py::TestMultiModalRAGAgent::test_routing_logic_text_only"]